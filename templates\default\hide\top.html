<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Welcome</title>
<link href="/css/default/jquery-ui.css" rel="stylesheet" type="text/css" />
<link href="/css/default/master.css?v=2126" rel="stylesheet" type="text/css" />
<link href="/css/default/layout.css?v=1.6" rel="stylesheet" type="text/css" />
<link href="/css/default/httop.css?v=1.2" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery-1.8.3.min.js"></script>
<script language="javascript" src="/js/jquery-1.11.3.js"></script>
<script language="javascript" src="/js/ui/jquery-ui.js"></script>
<script language="javascript">
function hideinfo(){ if(event.srcElement.tagName=="A"){
   window.status=event.srcElement.innerText}
}
document.onmouseover=hideinfo; 
document.onmousemove=hideinfo;
var globalpath = "{+$globalpath+}";  
function toggleSubMenu(element) {  
    var gid = element.getAttribute('gid'); // 获取当前游戏的 gid  
    var subMenuId = 'submenu_' + gid; // 构建子菜单的 id  
    var subMenu = document.getElementById(subMenuId); // 获取子菜单元素  
    if (subMenu.style.display === 'none' || subMenu.style.display === '') {  
        subMenu.style.display = 'block'; // 显示子菜单  
    } else {  
        subMenu.style.display = 'none'; // 隐藏子菜单  
    }  
} 
document.addEventListener('DOMContentLoaded', function() {  
    var scrollText = document.getElementById('notices');  
  
    // 鼠标移入时暂停滚动  
    scrollText.addEventListener('mouseenter', function() {  
        this.classList.add('paused');  
    });  
  
    // 鼠标移出时恢复滚动  
    scrollText.addEventListener('mouseleave', function() {  
        this.classList.remove('paused');  
    });  
  
    // 可选：点击时也可以暂停或恢复滚动（取决于您的需求）  
    scrollText.addEventListener('click', function() {  
        // 这里可以根据需要切换paused类，或者执行其他操作  
        // 例如，如果点击时您只想暂停而不恢复，那么您可能不需要这个事件监听器  
        // 但如果您想要一个切换效果，就像之前的示例那样，那么您应该保留它  
        // this.classList.toggle('paused');  
    });  
});
</script>
<link href="/css/default/ball.css" rel="stylesheet" type="text/css" />
</head>
<body id="topbody">
<script id=myjs language="javascript">var mulu='{+$mulu+}';var js=1;var sss='top';</script>

    <!-- 现代化卡片式头部容器 -->
    <div class="modern-header-container">
        <!-- 游戏选择卡片 -->
        <div class="game-selector-card">
            <div class="card-header">
                <h3 class="card-title">游戏选择</h3>
                <div class="current-game" id="currentGameDisplay">
                    <span class="game-name">{+$gamecs[0].gname+}</span>
                    <i class="dropdown-icon">▼</i>
                </div>
            </div>
            <ul class="games-dropdown" id="gamesDropdown" style="display: none;">
                {+section name=i loop=$gamecs+}
                <li class="game-item">
                    <a
                        gid="{+$gamecs[i].gid+}"
                        gname="{+$gamecs[i].gname+}"
                        fenlei="{+$gamecs[i].fenlei+}"
                        {+if $gid == $gamecs[i].gid+}class="xz active"{+/if+}
                        href="javascript:void(0)"
                    >
                        <span class="game-icon">🎮</span>
                        <span class="game-text">{+$gamecs[i].gname+}</span>
                    </a>
                </li>
                {+/section+}
            </ul>
        </div>

        <!-- 系统状态卡片 -->
        <div class="status-card">
            <div class="card-header">
                <h3 class="card-title">系统状态</h3>
                <div class="logo-section">
                    <span class="site-name">{+$webname+}</span>
                </div>
            </div>
            <div class="status-content">
                <div class="status-info-grid">
                    <div class="status-item">
                        <span class="status-label">当前期数:</span>
                        <label class="qishu status-value">{+$qishu+}</label>
                    </div>

                    <div class="status-item">
                        <span class="status-label">{+if $panstatus==1+}距关盘:{+else+}距开盘:{+/if+}</span>
                        <label class="time0 status-value countdown">{+$pantime+}</label>
                        <span class="panstatus" s='{+$panstatus+}' style="display: none;"></span>
                    </div>

                    {+if $gid==100+}
                    <div class="status-item otherstatus-item {+if $otherstatus!=1+}hide{+/if+}">
                        <span class="status-label">{+if $otherstatus==1+}距正码关盘:{+else+}距正码开盘:{+/if+}</span>
                        <label class="time1 status-value countdown">{+$othertime+}</label>
                        <span class="otherstatus" s='{+$otherstatus+}' style="display: none;"></span>
                    </div>
                    {+/if+}

                    <div class="status-item">
                        <span class="status-label">上期开奖:</span>
                        <label class="upqishu status-value" m='{+$upkj+}'>{+$upqishu+}</label>
                        <span class="status-label">期</span>
                    </div>
                </div>

                <div class="lottery-result">
                    <div class="upkj"></div>
                </div>

                <div class="control-actions">
                    <button class="control-btn qzclose" type="button">
                        <i class="btn-icon">🔒</i>
                        <span>关盘</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 用户信息卡片 -->
        <div class="user-info-card">
            <div class="card-header">
                <h3 class="card-title">用户信息</h3>
            </div>
            <div class="user-content">
                <div class="user-avatar">
                    <i class="user-icon">👤</i>
                </div>
                <div class="user-details">
                    <div class="user-name">管理员：{+$name+}</div>
                    <div class="online-info">
                        <a href="/hide/online.php?xtype=show" target="frame" class="online-link">
                            <span class="online-label">在线会员:</span>
                            <span class="online-count">{+$onlinenum+}</span>
                        </a>
                    </div>
                    <div class="message-center">
                        <a href="/hide/customer_manage.php?xtype=show" target="frame" id="customerServiceLink" class="message-link">
                            <i class="message-icon">💬</i>
                            <span>客服消息</span>
                            <span id="unreadBadge" class="message-badge" style="display: none;">0</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主菜单卡片网格 -->
    <div class="main-menu-container">
        <div class="menu-cards-grid">
            {+if $slib==1+}
            <div class="menu-card" data-menu="slib">
                <div class="card-icon">📊</div>
                <div class="card-content">
                    <h4 class="card-title">即时注单</h4>
                    <p class="card-desc">实时查看投注情况</p>
                </div>
                <a href="javascript:void(0);" class="card-link lib control" i=0 x="slib"></a>
            </div>
            {+/if+}

            {+if $kj==1+}
            <div class="menu-card" data-menu="kj">
                <div class="card-icon">🎯</div>
                <div class="card-content">
                    <h4 class="card-title">开奖管理</h4>
                    <p class="card-desc">开奖结果管理</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" x="kj" type='show'></a>
            </div>
            {+/if+}

            {+if $suser==1+}
            <div class="menu-card" data-menu="suser">
                <div class="card-icon">👥</div>
                <div class="card-content">
                    <h4 class="card-title">用户管理</h4>
                    <p class="card-desc">用户信息管理</p>
                </div>
                <a href="javascript:void(0);" class="card-link" x='suser' i=1></a>
            </div>
            {+/if+}

            <div class="menu-card" data-menu="now">
                <div class="card-icon">📋</div>
                <div class="card-content">
                    <h4 class="card-title">注单管理</h4>
                    <p class="card-desc">投注记录管理</p>
                </div>
                <a href="javascript:void(0)" class="card-link" target="frame" x='now' type='show'></a>
            </div>

            {+if $baox==1+}
            <div class="menu-card" data-menu="baox">
                <div class="card-icon">📈</div>
                <div class="card-content">
                    <h4 class="card-title">报表查询</h4>
                    <p class="card-desc">数据统计分析</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" x="baox"></a>
            </div>
            {+/if+}

            <div class="menu-card" data-menu="longs">
                <div class="card-icon">🏆</div>
                <div class="card-content">
                    <h4 class="card-title">开奖结果</h4>
                    <p class="card-desc">历史开奖查询</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" x="longs"></a>
            </div>

            <div class="menu-card" data-menu="caopan">
                <div class="card-icon">⚙️</div>
                <div class="card-content">
                    <h4 class="card-title">系统功能</h4>
                    <p class="card-desc">系统设置管理</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" i=3 x="caopan"></a>
            </div>

            {+if $hide==1+}
            <div class="menu-card" data-menu="check">
                <div class="card-icon">🔧</div>
                <div class="card-content">
                    <h4 class="card-title">高级功能</h4>
                    <p class="card-desc">高级系统功能</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" i=5 x="check"></a>
            </div>
            {+/if+}

            <div class="menu-card" data-menu="money" {+if $money!=1+}style='display:none;'{+/if+}>
                <div class="card-icon">💰</div>
                <div class="card-content">
                    <h4 class="card-title">现金管理</h4>
                    <p class="card-desc">资金流水管理</p>
                </div>
                <a href="javascript:void(0);" class="card-link xjgl" target="frame" i=4 x="money"></a>
            </div>

            <div class="menu-card" data-menu="customer">
                <div class="card-icon">🎧</div>
                <div class="card-content">
                    <h4 class="card-title">客服管理</h4>
                    <p class="card-desc">客户服务管理</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" x="customer_index"></a>
            </div>

            <div class="menu-card" data-menu="payment">
                <div class="card-icon">💳</div>
                <div class="card-content">
                    <h4 class="card-title">支付配置</h4>
                    <p class="card-desc">支付方式设置</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" x="payment_config"></a>
            </div>

            <div class="menu-card" data-menu="password">
                <div class="card-icon">🔐</div>
                <div class="card-content">
                    <h4 class="card-title">密码修改</h4>
                    <p class="card-desc">修改登录密码</p>
                </div>
                <a href="javascript:void(0);" class="card-link" target="frame" x="changepass2"></a>
            </div>

            <div class="menu-card logout-card" data-menu="logout">
                <div class="card-icon">🚪</div>
                <div class="card-content">
                    <h4 class="card-title">退出系统</h4>
                    <p class="card-desc">安全退出登录</p>
                </div>
                <a href="javascript:void(0);" class="card-link logout-link"></a>
            </div>
        </div>
    </div>
        {+if $libset==1+}<ul class="menu_sub"><li> </li></ul>{+/if+}
        {+if $suser==1+}<ul class="menu_sub"><li> </li></ul>{+/if+}
        {+if $baox==1+}<ul class="menu_sub"><li> </li></ul>{+/if+}
        <ul class="menu_sub">
            <li class="menu_sub_title">当前选中:<span>系统功能</span></li>
      <div class="container"> 
            {+if $buhuo==1+}
                       
            <a href="javascript:void(0)" class="button-style" target="frame" u="fly" type='show'>飞单跟投设置</a> 
            <a href="javascript:void(0)" class="button-style" target="frame" u="fly" type='flylist'>飞单记录</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='libset' type='show'>飞单限额</a>  
            {+/if+}
            {+if $liushui==1+}
            <a href="javascript:void(0)" class="button-style" target="frame" u="fly" type='shui'>赚分设置</a>                   
            {+/if+}
            {+if $libset==1+}         
            <a href="javascript:void(0)" class="button-style" target="frame" u='libset' type='warn'>警示金额</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='libset' type='auto'>自动降倍</a> 
            {+/if+}
            {+if $now==1+}
            {+/if+}
            {+if $zshui==1+}
            <a href="javascript:void(0)" class="button-style" target="frame" u='zshui' type='ma'>号码属性</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='zshui' type='ptype'>默认赔率</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='zshui' type='show'>默认退水</a> 
            <a href="javascript:void(0)" class="button-style" target="frame" u='zshui' type='setattshow'>赔率参数</a>   
            {+/if+}
            {+if $news==1+}
            <a href="javascript:void(0)" class="button-style" target="frame" u='news' type='show'>消息</a>          
            {+/if+}
            {+if ($hide==1 || ($caopan==1 && $xxtz2==1))+}
            <!--<a href="javascript:void(0)" class="button-style" target="frame" u='xxtz2' type='show'>注单删改</a> -->
            <a href="javascript:void(0)" class="button-style" target="frame" u='zdcx' type='show'>注单删改</a> 
            {+/if+}
            {+if ($hide==1 | $caopan==1)+}
            <a href="javascript:void(0)" class="button-style" target="frame" u='caopan' type='show'>系统管理员</a>  
            {+if $err==1+}<a href="javascript:void(0)" class="button-style" target="frame" u='err' type='show'>异常注单</a>{+/if+}
            <a href="javascript:void(0)" class="button-style" target="frame" u='history' type='show'>记录管理</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='sysconfig' type='show'>参数</a> 
            <a href="javascript:void(0)" class="button-style" target="frame" u='online' type='show'>在线</a>
            {+/if+}
            
           
            </li>
        </ul>
    
        <ul class="menu_sub" {+if $money!=1+}style='display:none;'{+/if+}>
            <li class="menu_sub_title">当前选中:<span>现金管理</span></li>
            <a href="javascript:void(0)" class="button-style" target="frame" u='money' type='moneyuser'>现金会员</a>
            <a href="javascript:void(0)" class="button-style" target="frame" u='money' type='chongzhi'>充值管理</a>
            <a href="javascript:void(0)" class="button-style" target="frame" u='money' type='tikuan'>提现管理</a>
            <a href="javascript:void(0)" class="button-style usdt-rate-btn" onclick="openUsdtRate()">
                <i class="fas fa-coins"></i> USDT汇率管理
            </a>
            <a href="javascript:void(0)" class="button-style" target="frame" u='money' type='bank'>银行</a>
            <a href="javascript:void(0)" class="button-style" target="frame" u='money' type='chongzhifs'>充值方式</a>
            <a href="javascript:void(0)" target="frame" u='money' type='banknum'>收款帐户</a>
            <a href="javascript:void(0)" class="button-style" target="frame" u='money' type='notices'>消息管理</a>

            </li>
        </ul>


       {+if $hide==1+}
        <ul class="menu_sub">             
            <li class="menu_sub_title">当前选中:<span>高级功能</span></li>
            <a href="javascript:void(0);" target="frame" u="baox" type='oldshow'>报表查询</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='xxtz' type='show'>注单明细</a>    
            <a href="javascript:void(0)" class="button-style" target="frame" u='now' type='show'>注单管理</a>   
            <!--<a href="javascript:void(0)" class="button-style" target="frame" u='admins' type='list'>管理员</a>     -->
            <a href="javascript:void(0)" class="button-style" target="frame" u='webconfig' type='show'>网站配置</a>                      
            <a href="javascript:void(0)" class="button-style" target="frame" u='game' type='show'>游戏配置</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='zshui' type='gameset'>彩种开放</a>  
                  
            <a href="javascript:void(0)" class="button-style" target="frame" u='class' type='classpan'>玩法归类</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='class' type='bigclass'>大分类</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='class' type='sclass'>小分类</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='class' type='class'>玩法分类</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='play' type='show'>玩法列表</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='err' type='show'>异常注单</a> 
                  
            {+if $hides==1+} 
    
            <a href="javascript:void(0)" class="button-style" target="frame" u='check' type='show'>检测</a>     
            <a href="javascript:void(0)" class="button-style" target="frame" u='message' type='show'>会员反馈</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='play' type='downlist'>下载记录</a>  
            <a href="javascript:void(0)" class="button-style" target="frame" u='loglist' type='loglist'>注单记录</a> 
            {+/if+}
            </li>
        </ul>  
        {+/if+}

  

   </div> 
    <div id="contents" >
        <iframe id="frame" name="frame" src='/hide/new.php' frameborder="0"></iframe>
    </div>
    <div id="footer" class="footer">  
    <div class="notice">  
        <span id="notices" class="scroll-text">这是一条会滚动的消息，它将从左向右持续滚动。</span>  
    </div>  
    <a href="javascript:void(0);" class="button-style more" target="frame">更多</a>  
   </div>  

<div id="dialog" title="您有新的交易请求" style="display:none;">
  <p style="text-align:center"><button class="clqq s1">前往处理</button></p>
</div>

<!-- 客服悬浮组件 -->
<div id="customerServiceFloat" class="customer-service-float">
    <div class="float-main" id="floatMain">
        <div class="float-icon">
            <i class="service-icon">💬</i>
            <span class="float-badge" id="floatBadge" style="display: none;">0</span>
        </div>
        <div class="float-text">客服</div>
    </div>

    <!-- 展开的功能面板 -->
    <div class="float-panel" id="floatPanel" style="display: none;">
        <div class="panel-header">
            <span class="panel-title">客服中心</span>
            <button class="panel-close" onclick="toggleFloatPanel()">&times;</button>
        </div>
        <div class="panel-content">
            <div class="service-item" onclick="openCustomerChat()">
                <div class="item-icon chat-icon">💬</div>
                <div class="item-info">
                    <div class="item-title">客服聊天</div>
                    <div class="item-desc">实时消息管理</div>
                </div>
                <div class="item-badge" id="chatBadge" style="display: none;">0</div>
            </div>

            <div class="service-item" onclick="openCustomerManage()">
                <div class="item-icon manage-icon">👥</div>
                <div class="item-info">
                    <div class="item-title">客服管理</div>
                    <div class="item-desc">用户消息管理</div>
                </div>
            </div>

            <div class="service-item" onclick="openPaymentConfig()">
                <div class="item-icon payment-icon">💳</div>
                <div class="item-info">
                    <div class="item-title">支付配置</div>
                    <div class="item-desc">支付方式设置</div>
                </div>
            </div>

            <div class="service-item" onclick="openUsdtRate()">
                <div class="item-icon rate-icon">💰</div>
                <div class="item-info">
                    <div class="item-title">汇率管理</div>
                    <div class="item-desc">USDT汇率设置</div>
                </div>
            </div>

            <div class="service-item" onclick="openWithdrawManage()">
                <div class="item-icon withdraw-icon">📤</div>
                <div class="item-info">
                    <div class="item-title">提现管理</div>
                    <div class="item-desc">提现审核处理</div>
                </div>
            </div>

            <div class="service-item" onclick="openRechargeManage()">
                <div class="item-icon recharge-icon">📥</div>
                <div class="item-info">
                    <div class="item-title">充值管理</div>
                    <div class="item-desc">充值记录管理</div>
                </div>
            </div>
        </div>
    </div>
</div>
<script language="javascript" id='zhishu'>
var ngid={+$gid+};
var fenlei = {+$fenlei+};
var layer={+$layer+};
ma = [];
     ma['紅'] = new Array(01,02,07,08,12,13,18,19,23,24,29,30,34,35,40,45,46);
     ma['藍'] = new Array(03,04,09,10,14,15,20,25,26,31,36,37,41,42,47,48);
     ma['綠'] = new Array(05,06,11,16,17,21,22,27,28,32,33,38,39,43,44,49);

// 客服消息检查
function checkUnreadMessages() {
    $.ajax({
        type: 'GET',
        url: mulu + 'customer_manage.php?xtype=messages&unread_only=1&limit=1',
        dataType: 'json',
        cache: false,
        success: function(res) {
            if (res && res.code === 1 && res.data) {
                var unreadCount = res.data.unread_count || 0;

                var badge = document.getElementById('unreadBadge');
                if (badge) {
                    if (unreadCount > 0) {
                        badge.style.display = 'inline-block';
                        badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                        // 添加闪烁效果
                        badge.style.animation = 'blink 1s infinite';
                    } else {
                        badge.style.display = 'none';
                        badge.style.animation = 'none';
                    }
                }
            }
        },
        error: function() {
            // 静默处理错误
        }
    });
}

// 页面加载完成后开始检查
$(document).ready(function() {
    // 立即检查一次
    checkUnreadMessages();

    // 每30秒检查一次未读消息
    setInterval(checkUnreadMessages, 30000);

    // 初始化客服悬浮组件
    initCustomerServiceFloat();
});

// 客服悬浮组件功能
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
let isPanelOpen = false;

function initCustomerServiceFloat() {
    const floatElement = document.getElementById('customerServiceFloat');
    const floatMain = document.getElementById('floatMain');
    const floatPanel = document.getElementById('floatPanel');

    if (!floatElement || !floatMain) return;

    // 从localStorage恢复位置
    const savedPosition = localStorage.getItem('customerFloatPosition');
    if (savedPosition) {
        const position = JSON.parse(savedPosition);
        floatElement.style.bottom = position.bottom + 'px';
        floatElement.style.right = position.right + 'px';
    }

    // 鼠标按下事件
    floatMain.addEventListener('mousedown', function(e) {
        e.preventDefault();
        isDragging = true;

        const rect = floatElement.getBoundingClientRect();
        dragOffset.x = e.clientX - rect.left;
        dragOffset.y = e.clientY - rect.top;

        floatElement.classList.add('dragging');

        // 如果面板是打开的，先关闭它
        if (isPanelOpen) {
            floatPanel.style.display = 'none';
            isPanelOpen = false;
        }
    });

    // 鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        e.preventDefault();

        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const elementWidth = floatElement.offsetWidth;
        const elementHeight = floatElement.offsetHeight;

        let newX = windowWidth - (e.clientX - dragOffset.x + elementWidth);
        let newY = windowHeight - (e.clientY - dragOffset.y + elementHeight);

        // 边界限制
        newX = Math.max(10, Math.min(newX, windowWidth - elementWidth - 10));
        newY = Math.max(10, Math.min(newY, windowHeight - elementHeight - 10));

        floatElement.style.right = newX + 'px';
        floatElement.style.bottom = newY + 'px';
    });

    // 鼠标释放事件
    document.addEventListener('mouseup', function(e) {
        if (!isDragging) return;

        isDragging = false;
        floatElement.classList.remove('dragging');

        // 保存位置到localStorage
        const rect = floatElement.getBoundingClientRect();
        const position = {
            right: window.innerWidth - rect.right,
            bottom: window.innerHeight - rect.bottom
        };
        localStorage.setItem('customerFloatPosition', JSON.stringify(position));
    });

    // 点击事件（非拖拽时）
    floatMain.addEventListener('click', function(e) {
        if (!isDragging) {
            toggleFloatPanel();
        }
    });

    // 点击外部关闭面板
    document.addEventListener('click', function(e) {
        if (!floatElement.contains(e.target) && isPanelOpen) {
            floatPanel.style.display = 'none';
            isPanelOpen = false;
        }
    });

    // 更新悬浮组件的未读消息数量
    updateFloatBadge();
    setInterval(updateFloatBadge, 30000);
}

function toggleFloatPanel() {
    const floatPanel = document.getElementById('floatPanel');

    if (isPanelOpen) {
        floatPanel.style.display = 'none';
        isPanelOpen = false;
    } else {
        floatPanel.style.display = 'block';
        isPanelOpen = true;
    }
}

function updateFloatBadge() {
    const floatBadge = document.getElementById('floatBadge');
    const chatBadge = document.getElementById('chatBadge');
    const unreadBadge = document.getElementById('unreadBadge');

    // 从现有的未读消息数量获取
    if (unreadBadge && unreadBadge.style.display !== 'none') {
        const count = unreadBadge.textContent;
        if (floatBadge) {
            floatBadge.textContent = count;
            floatBadge.style.display = 'inline-block';
        }
        if (chatBadge) {
            chatBadge.textContent = count;
            chatBadge.style.display = 'inline-block';
        }
    } else {
        if (floatBadge) floatBadge.style.display = 'none';
        if (chatBadge) chatBadge.style.display = 'none';
    }
}

// 各个功能页面跳转函数
function openCustomerChat() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/customer_chat.php?xtype=show';
    }
    toggleFloatPanel();
}

function openCustomerManage() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/customer_manage.php?xtype=show';
    }
    toggleFloatPanel();
}

function openPaymentConfig() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/payment_config.php?xtype=show';
    }
    toggleFloatPanel();
}

function openUsdtRate() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/usdt_rate.php?xtype=show';
    }
    toggleFloatPanel();
}

function openWithdrawManage() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/withdraw.php?xtype=show';
    }
    toggleFloatPanel();
}

function openRechargeManage() {
    const frame = document.getElementById('frame');
    if (frame) {
        frame.src = '/hide/recharge.php?xtype=show';
    }
    toggleFloatPanel();
}

</script>

<style>
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

#unreadBadge {
    box-shadow: 0 0 5px rgba(255, 68, 68, 0.5);
}

/* 客服悬浮组件样式 */
.customer-service-float {
    position: fixed;
    bottom: 80px;
    right: 30px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.float-main {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    user-select: none;
}

.float-main:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
}

.float-main:active {
    transform: scale(0.95);
}

.float-icon {
    position: relative;
    margin-bottom: 2px;
}

.service-icon {
    font-size: 20px;
    color: #fff;
    font-style: normal;
}

.float-text {
    font-size: 10px;
    color: #fff;
    font-weight: 600;
    text-align: center;
}

.float-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4444;
    color: #fff;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
    animation: pulse 2s infinite;
    box-shadow: 0 2px 8px rgba(255, 68, 68, 0.4);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 功能面板样式 */
.float-panel {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 280px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
    transform-origin: bottom right;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-title {
    font-size: 14px;
    font-weight: 600;
}

.panel-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s;
}

.panel-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.panel-content {
    max-height: 400px;
    overflow-y: auto;
}

.service-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background 0.2s;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    position: relative;
}

.service-item:last-child {
    border-bottom: none;
}

.service-item:hover {
    background: rgba(102, 126, 234, 0.05);
}

.item-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 12px;
    flex-shrink: 0;
}

.chat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.manage-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.payment-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.rate-icon {
    background: linear-gradient(135deg, #fa709a, #fee140);
}

.withdraw-icon {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.recharge-icon {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.item-info {
    flex: 1;
}

.item-title {
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.item-desc {
    font-size: 11px;
    color: #7f8c8d;
}

.item-badge {
    background: #ff4444;
    color: #fff;
    border-radius: 8px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    text-align: center;
}

/* 拖拽状态 */
.customer-service-float.dragging {
    transition: none;
}

.customer-service-float.dragging .float-main {
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
}

/* 响应式适配 */
@media (max-width: 768px) {
    .customer-service-float {
        bottom: 60px;
        right: 20px;
    }

    .float-main {
        width: 50px;
        height: 50px;
    }

    .service-icon {
        font-size: 18px;
    }

    .float-text {
        font-size: 9px;
    }

    .float-panel {
        width: 260px;
        bottom: 60px;
    }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
    width: 4px;
}

.panel-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.panel-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
</body>
</html>
